steps:
  # Step 1: Install root dependencies (includes workspaces)
  - name: 'us-west1-docker.pkg.dev/gemini-code-dev/gemini-code-containers/gemini-code-builder'
    entrypoint: 'npm'
    args: ['install']

  # Step 2: Update version in root package.json
  - name: 'us-west1-docker.pkg.dev/gemini-code-dev/gemini-code-containers/gemini-code-builder'
    entrypoint: 'bash'
    args:
      - -c # Use bash -c to allow for command substitution and string manipulation
      - |
        current_version=$(npm pkg get version | sed 's/"//g')
        new_version="$${current_version}-$SHORT_SHA.$_REVISION"
        npm pkg set "version=$${new_version}"
        echo "Set root package.json version to: $${new_version}"

  # Step 3: Run prerelease:dev to update workspace versions and dependencies
  - name: 'us-west1-docker.pkg.dev/gemini-code-dev/gemini-code-containers/gemini-code-builder'
    entrypoint: 'npm'
    args: ['run', 'prerelease:dev'] # This will run prerelease:version and prerelease:deps

  # Step 4: Authenticate for Docker and NPM
  - name: 'us-west1-docker.pkg.dev/gemini-code-dev/gemini-code-containers/gemini-code-builder'
    entrypoint: 'npm'
    args: ['run', 'auth']

  # Step 5: Run the master release script
  - name: 'us-west1-docker.pkg.dev/gemini-code-dev/gemini-code-containers/gemini-code-builder'
    entrypoint: 'npm'
    args: ['run', 'publish:release']
    env:
      - 'GEMINI_SANDBOX=$_CONTAINER_TOOL'
      - 'SANDBOX_IMAGE_REGISTRY=$_SANDBOX_IMAGE_REGISTRY'
      - 'SANDBOX_IMAGE_NAME=$_SANDBOX_IMAGE_NAME'
      - 'NPM_PUBLISH_TAG=$_NPM_PUBLISH_TAG'

options:
  defaultLogsBucketBehavior: REGIONAL_USER_OWNED_BUCKET
  dynamicSubstitutions: true

substitutions:
  _REVISION: '0'
  _SANDBOX_IMAGE_REGISTRY: 'us-west1-docker.pkg.dev/gemini-code-dev/gemini-cli'
  _SANDBOX_IMAGE_NAME: 'sandbox-ci'
  _NPM_PUBLISH_TAG: 'head'
  _CONTAINER_TOOL: 'docker'
